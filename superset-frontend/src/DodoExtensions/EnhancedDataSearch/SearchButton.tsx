import { useEffect, useState } from 'react';
import { styled, keyframes, t } from '@superset-ui/core';

interface SearchMorphProps {
  handleClick: () => void;
  placeholder?: string;
  state?: 'open' | 'closed';
}

type SearchState = 'open' | 'closed' | 'opening' | 'closing';

// eslint-disable-next-line theme-colors/no-literal-colors
const searchExpand = keyframes`
  0% {
    color: transparent;
    width: 28px;
  }
  20% {
    width: 28px;
  }
  45% {
    width: 200px;
  }
  99% {
    color: transparent;
  }
  100% {
    width: 200px;
    color: #666666;
  }
`;

const beforeMagic = keyframes`
  0% {}
  40% {
    opacity: 0;
  }
  55% {
    opacity: 1;
    width: 10px;
    top: -3px;
    right: -2px;
  }
  65% {
    width: 22px;
    top: 5px;
    right: 8px;
  }
  80% {
    width: 15px;
    top: 7px;
    right: 11px;
  }
  100% {
    width: 15px;
    top: 7px;
    right: 11px;
    opacity: 1;
  }
`;

const afterMagic = keyframes`
  0% {}
  10% {
    width: 18px;
    bottom: -8px;
    right: -8px;
  }
  15% {
    opacity: 1;
  }
  25% {
    opacity: 0;
  }
  35% {
    width: 10px;
    bottom: -2px;
    right: -2px;
    opacity: 0;
  }
  64% {
    opacity: 0;
  }
  65% {
    opacity: 1;
    width: 10px;
    bottom: -1px;
    right: -2px;
  }
  75% {
    width: 22px;
    bottom: 3px;
    right: 8px;
  }
  100% {
    width: 15px;
    bottom: 9px;
    right: 11px;
  }
`;

// Стили через styled-components
const SearchMorphContainer = styled.div`
  width: 200px;
  display: flex;
  justify-content: flex-end;
  padding: 0;
`;

const SearchMorphWrapper = styled.div`
  position: relative;
  width: 208px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const SearchMorph = styled.button<{ state: SearchState }>`
  width: ${({ state }) => (state === 'open' ? '200px' : '28px')};
  height: 28px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  transition: all 0.15s;
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
  color: ${({ state, theme }) =>
    state === 'open' ? theme.colors.grayscale.base : 'transparent'};

  animation-duration: 1.1s;
  animation-fill-mode: forwards;

  ${props =>
    props.state === 'opening' &&
    `
      animation-name: ${searchExpand};
    `}

  ${props =>
    props.state === 'closing' &&
    `
      animation-name: ${searchExpand};
      animation-direction: reverse;
    `}
`;

const SearchInput = styled.div<{ state: SearchState }>`
  width: 100%;
  height: 100%;
  background: none;
  border: 1px solid ${props => props.theme.colors.grayscale.base};
  border-radius: 20px;
  box-sizing: border-box;
  padding: 0 12px;
  font-size: 12px;
  color: ${({ state, theme }) =>
    state === 'open' ? theme.colors.grayscale.base : 'transparent'};
  z-index: 2;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  transition: color 0.3s ease;

  ${props =>
    props.state === 'opening' &&
    `
      color: transparent;
      transition-delay: 0.5s;
    `}
`;

const SearchIconContainer = styled.div<{ state: SearchState }>`
  width: 28px;
  height: 28px;
  position: absolute;
  top: 1px;
  right: 0;
  z-index: ${props => (props.state === 'open' ? 3 : 1)};
  cursor: pointer;
`;

const SearchIconBefore = styled.div<{ state: SearchState }>`
  content: '';
  width: ${props => (props.state === 'open' ? '15px' : '10px')};
  height: 1px;
  background-color: ${({ theme }) => theme.colors.grayscale.base};
  border-radius: 1px;
  position: absolute;
  transform-origin: 100% 100%;
  top: ${props => (props.state === 'open' ? '7px' : '-4px')};
  right: ${props => (props.state === 'open' ? '11px' : '-4px')};
  opacity: ${props => (props.state === 'open' ? '1' : '0')};
  transform: rotate(-45deg);

  animation-duration: 1.1s;
  animation-fill-mode: forwards;

  ${props =>
    props.state === 'opening' &&
    `
      animation-name: ${beforeMagic};
    `}

  ${props =>
    props.state === 'closing' &&
    `
      animation-name: ${beforeMagic};
      animation-direction: reverse;
    `}
`;

const SearchIconAfter = styled.div<{ state: SearchState }>`
  content: '';
  width: ${props => (props.state === 'open' ? '15px' : '10px')};
  height: 1px;
  background-color: ${({ theme }) => theme.colors.grayscale.base};
  border-radius: 1px;
  position: absolute;
  transform-origin: 100% 100%;
  bottom: ${props => (props.state === 'open' ? '9px' : '-4px')};
  right: ${props => (props.state === 'open' ? '11px' : '-4px')};
  transform: rotate(45deg);

  animation-duration: 1.1s;
  animation-fill-mode: forwards;

  ${props =>
    props.state === 'opening' &&
    `
      animation-name: ${afterMagic};
    `}

  ${props =>
    props.state === 'closing' &&
    `
      animation-name: ${afterMagic};
      animation-direction: reverse;
    `}
`;

const SearchButton = ({
  handleClick,
  placeholder = t('Search'),
  state = 'closed',
}: SearchMorphProps) => {
  const [searchState, setSearchState] = useState<SearchState>('closed');

  useEffect(() => {
    if (state === 'closed') {
      setSearchState('opening');
      setTimeout(() => {
        setSearchState('open');
      }, 1100);
    } else if (state === 'open') {
      setSearchState('closing');
      setTimeout(() => {
        setSearchState('closed');
      }, 1100);
    }
  }, [state]);

  return (
    <SearchMorphContainer>
      <SearchMorphWrapper>
        <SearchMorph
          className="search-button"
          state={searchState}
          onClick={handleClick}
        >
          <SearchInput state={searchState}>
            {searchState === 'open' && placeholder}
          </SearchInput>
          <SearchIconContainer state={searchState}>
            <SearchIconBefore state={searchState} />
            <SearchIconAfter state={searchState} />
          </SearchIconContainer>
        </SearchMorph>
      </SearchMorphWrapper>
    </SearchMorphContainer>
  );
};

export default SearchButton;
